import React, { useState, useEffect } from 'react';
import PromptEditor from './components/PromptEditor';
import EvaluationRunner from './components/EvaluationRunner';
import LGDEvaluationRunner from './components/LGDEvaluationRunner';
import ResultsTable from './components/ResultsTable';
import LGDResultsTable from './components/LGDResultsTable';
import Sidebar from './components/Sidebar';
import { promptsApi, evaluationsApi, lgdEvaluationsApi } from './services/api';

const App = () => {
  const [prompts, setPrompts] = useState([]);
  const [evaluations, setEvaluations] = useState([]);
  const [lgdEvaluations, setLgdEvaluations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeEvalType, setActiveEvalType] = useState('idp'); // 'idp' or 'lgd'

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [promptsResponse, evaluationsResponse] = await Promise.all([
        promptsApi.getAll(),
        evaluationsApi.getAll()
      ]);

      // Try to load LGD evaluations, but don't fail if the table doesn't exist yet
      let lgdEvaluationsResponse = { data: [] };
      try {
        lgdEvaluationsResponse = await lgdEvaluationsApi.getAll();
      } catch (lgdError) {
        console.warn('LGD evaluations not available yet:', lgdError);
      }

      setPrompts(promptsResponse.data);
      setEvaluations(evaluationsResponse.data);
      setLgdEvaluations(lgdEvaluationsResponse.data);
      setError(null);
    } catch (err) {
      setError('Failed to load data');
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePromptUpdate = async (id, content) => {
    try {
      const response = await promptsApi.update(id, content);
      setPrompts(prev => prev.map(p => p.id === id ? response.data : p));
      return response.data;
    } catch (err) {
      console.error('Error updating prompt:', err);
      throw err;
    }
  };

  const handleEvaluationRun = async (input) => {
    try {
      const response = await evaluationsApi.run(input);
      setEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running evaluation:', err);
      throw err;
    }
  };

  const handleLGDEvaluationRun = async (input) => {
    try {
      const response = await lgdEvaluationsApi.run(input);
      setLgdEvaluations(prev => [response.data, ...prev]);
      return response.data;
    } catch (err) {
      console.error('Error running LGD evaluation:', err);

      // Re-throw with more context for the component to handle
      if (err.response?.status === 504) {
        const error = new Error('LGD evaluation timed out');
        error.response = err.response;
        throw error;
      }

      throw err;
    }
  };

  const handleEvaluationUpdate = (updatedEvaluation) => {
    setEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  const handleLGDEvaluationUpdate = (updatedEvaluation) => {
    setLgdEvaluations(prev =>
      prev.map(evaluation => evaluation.id === updatedEvaluation.id ? updatedEvaluation : evaluation)
    );
  };

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
        <h2>Error: {error}</h2>
        <button onClick={loadData}>Retry</button>
      </div>
    );
  }

  // Filter prompts based on active evaluation type
  const getFilteredPrompts = () => {
    if (activeEvalType === 'idp') {
      return prompts.filter(p => p.id <= 2); // IDP prompts (1, 2)
    } else {
      return prompts.filter(p => p.id === 3); // LGD prompts (only 3, hide 4)
    }
  };

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      <Sidebar
        activeEvalType={activeEvalType}
        onEvalTypeChange={setActiveEvalType}
      />

      <div style={{ flex: 1, padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
        <h1 style={{ textAlign: 'center', marginBottom: '30px' }}>
          {activeEvalType === 'idp' ? 'IDP Recommendation' : 'LGD Analysis'} Prompt Evaluations
        </h1>

        <div style={{ display: 'grid', gap: '20px', marginBottom: '30px' }}>
          <div
            className="prompt-grid"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
              gap: '20px'
            }}>
            {getFilteredPrompts().map(prompt => (
              <PromptEditor
                key={prompt.id}
                prompt={prompt}
                onUpdate={handlePromptUpdate}
              />
            ))}
          </div>

          {activeEvalType === 'idp' ? (
            <EvaluationRunner onRun={handleEvaluationRun} />
          ) : (
            <LGDEvaluationRunner onRun={handleLGDEvaluationRun} />
          )}
        </div>

        {activeEvalType === 'idp' ? (
          <ResultsTable evaluations={evaluations} onEvaluationUpdate={handleEvaluationUpdate} />
        ) : (
          <LGDResultsTable evaluations={lgdEvaluations} onEvaluationUpdate={handleLGDEvaluationUpdate} />
        )}
      </div>
    </div>
  );
};

export default App;
