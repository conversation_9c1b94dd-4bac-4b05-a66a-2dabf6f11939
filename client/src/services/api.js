import axios from 'axios';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds default timeout
});

const plainApi = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds default timeout
});

// Create a separate axios instance for long-running evaluation requests
const evaluationApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 900000, // 15 minutes timeout for evaluations
});

export const promptsApi = {
  getAll: () => api.get('/prompts'),
  getById: (id) => api.get(`/prompts/${id}`),
  getVersion: (id, version) => api.get(`/prompts/${id}/versions/${version}`),
  update: (id, content) => api.put(`/prompts/${id}`, { content }),
};

export const evaluationsApi = {
  getAll: () => api.get('/evaluations'),
  run: (input) => evaluationApi.post('/evaluations/run', { input }), // Use long timeout for evaluation runs
  updateAnnotation: (id, annotation) => api.put(`/evaluations/${id}/annotation`, { annotation }),
};

export const lgdEvaluationsApi = {
  getAll: () => api.get('/lgd-evaluations'),
  run: (input) => evaluationApi.post('/lgd-evaluations/run', { input }), // Use long timeout for LGD evaluation runs
  updateAnnotation: (id, annotation) => api.put(`/lgd-evaluations/${id}/annotation`, { annotation }),
};

export const dataApi = {
  getSampleTranscript: () => plainApi.get('/data/lgd_transcript.txt'),
  getSampleCompetencies: () => plainApi.get('/data/lgd_competency_guidelines.txt')
};

export default api;
