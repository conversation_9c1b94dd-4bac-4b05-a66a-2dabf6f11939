class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config, timeoutMs = 300000) { // 5 minute default timeout
    try {
      await this.initPromise; // Ensure client is initialized

      // Create a timeout promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Request timed out after ${timeoutMs}ms`)), timeoutMs);
      });

      // Race between the API call and timeout
      const apiPromise = this.client.models.generateContent({
        model: model,
        contents: prompt,
        config: config
      });

      const response = await Promise.race([apiPromise, timeoutPromise]);
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      if (error.message.includes('timed out')) {
        throw new Error(`Gemini API request timed out after ${timeoutMs}ms`);
      }
      throw new Error(`Failed to generate response from Gemini: ${error.message}`);
    }
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    const startTime = Date.now();
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;
      console.log(`Starting LGD prompt chain for transcript of length: ${transcript.length} characters`);

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{lgd_competencies}}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the transcripts:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log('Step 1: Running LGD analysis prompt...');
      const step1StartTime = Date.now();
      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-05-06',
        analysisUserPrompt,
        analysisConfig,
        600000 // 10 minute timeout for analysis step
      );
      console.log(`Step 1 completed in ${Date.now() - step1StartTime}ms`);

      // Step 2: Run formatting prompt with analysis output
      const formattingUserPrompt = `
        You need to format this text content to JSON format
        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      console.log('Step 2: Running LGD formatting prompt...');
      const step2StartTime = Date.now();
      const finalOutput = await this.generateResponse(
        'gemini-2.5-flash-preview-05-20',
        formattingUserPrompt,
        formattingConfig,
        300000 // 5 minute timeout for formatting step
      );
      console.log(`Step 2 completed in ${Date.now() - step2StartTime}ms`);

      const totalTime = Date.now() - startTime;
      console.log(`LGD prompt chain completed successfully in ${totalTime}ms`);

      return {
        step1: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: finalOutput
        },
        finalOutput,
        processingTime: totalTime
      };
    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error(`Error in LGD prompt chain after ${totalTime}ms:`, error);

      // Provide more specific error messages
      if (error.message.includes('timed out')) {
        throw new Error(`LGD evaluation timed out after ${totalTime}ms. This may be due to a large transcript or slow API response. Please try again or contact support.`);
      }

      throw new Error(`LGD evaluation failed: ${error.message}`);
    }
  }
}

module.exports = new LGDGeminiService();
