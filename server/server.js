const express = require('express');
const cors = require('cors');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

const promptRoutes = require('./routes/prompts');
const evaluationRoutes = require('./routes/evaluations');
const lgdEvaluationRoutes = require('./routes/lgdEvaluations');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' })); // Increase JSON payload limit for large transcripts

// Request timeout middleware - set to 15 minutes for LGD evaluations
app.use((req, res, next) => {
  // Set longer timeout for evaluation endpoints
  if (req.path.includes('/evaluations/run') || req.path.includes('/lgd-evaluations/run')) {
    req.setTimeout(900000); // 15 minutes
    res.setTimeout(900000); // 15 minutes
  } else {
    req.setTimeout(30000); // 30 seconds for other endpoints
    res.setTimeout(30000); // 30 seconds for other endpoints
  }
  next();
});

// Routes

// Basic Authentication Middleware
const basicAuth = require('express-basic-auth');

const authMiddleware = basicAuth({
  users: { [process.env.BASIC_AUTH_USERNAME]: process.env.BASIC_AUTH_PASSWORD },
  challenge: true, // Show authentication dialog
  unauthorizedResponse: 'Unauthorized'
});

// Apply basic auth to all routes except health check
app.use('/api/*', (req, res, next) => {
  if (req.path === '/api/health') {
    return next();
  }
  authMiddleware(req, res, next);
});

app.use('/api/prompts', promptRoutes);
app.use('/api/evaluations', evaluationRoutes);
app.use('/api/lgd-evaluations', lgdEvaluationRoutes);

// Serve static files from data directory
app.use('/api/data', express.static(path.join(__dirname, 'data')));

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Server is running' });
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
